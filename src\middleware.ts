/* ------------------------------------------------------------------
 * Global Edge middleware
 * -----------------------------------------------------------------*/
import { NextRequest, NextResponse } from 'next/server';
import createMiddleware from 'next-intl/middleware';
import { routing } from '@/lib/i18n/routing';
import { defaultCountry } from '@/lib/i18n/locales';

// Create the next-intl handler
const handleI18nRouting = createMiddleware(routing);

/* ------------------------------------------------------------------
* Only run for “real” pages – skip static assets etc.
* -----------------------------------------------------------------*/
export const config = {
  // Match only internationalized pathnames, exclude /insights routes
  matcher: [
    '/',
    '/(en|de|fr|es|pt|hr|el)/:path*',
    // Exclude only /insights routes from i18n handling
    '/((?!insights).*)'
  ]
};

/**
 * Middleware to enhance incoming requests with client IP and location info.
 *
 * - Sets `x-ip` header with the client IP.
 * - Persists detected country code in a `locale` cookie.
 *
 * @param req - Incoming request handled by Next.js Edge Middleware.
 * @returns Modified NextResponse with enriched headers and cookies.
 */
export function middleware(req: NextRequest): NextResponse {
  /* ----- 1.  Gather information ----------------------------------- */
  const ip = req.ip ?? '';
  const country = req.geo?.country ?? defaultCountry;

  /* ----- 2.  Skip i18n handling for insights routes --------------- */
  if (req.nextUrl.pathname.startsWith('/insights')) {
    // Let the rewrite in next.config.mjs handle this
    const response = NextResponse.next();
    response.cookies.set('locale', country);
    response.headers.set('x-ip', ip);
    return response;
  }

  /* ----- 3.  Enrich the *request* headers with the client IP ------- */
  const headers = new Headers(req.headers);
  headers.set('x-ip', ip);

  /* ----- 4.  Let next-intl decide about locale-prefixed routes ----- */
  const response = handleI18nRouting(new NextRequest(req.nextUrl, { headers }));

  /* ----- 5.  Persist data & expose IP on the *response* ------------ */
  response.cookies.set('locale', country);
  response.headers.set('x-ip', ip);

  // Return the modified response
  return response;
}