import { getSiteConfig } from "../../site.config";
const siteConfig = getSiteConfig();

// Translation key mappings for menu items
export const menuItemTranslationKeys: Record<string, string> = {
  'home': 'navigation.main_menu.home',
  'login': 'navigation.main_menu.login',
  'faq': 'navigation.main_menu.faq',
  'insights': 'navigation.main_menu.insights',
  'about-us': 'navigation.main_menu.about_us',
  'pricing': 'navigation.main_menu.pricing',
  'account': 'navigation.main_menu.my_account',
  'academy': 'navigation.main_menu.academy',
};

export const accountMenuTranslationKeys: Record<string, string> = {
  'history': 'navigation.account_menu.test_history',
  'account': 'navigation.account_menu.my_information',
  'change-password': 'navigation.account_menu.change_password',
  'offer': 'navigation.account_menu.my_subscription',
};

export const legalMenuTranslationKeys: Record<string, string> = {
  'privacy-policy': 'navigation.legal_menu.privacy_policy',
  'terms-conditions': 'navigation.legal_menu.terms_conditions',
  'cookie-policy': 'navigation.legal_menu.cookie_policy',
  'refund-policy': 'navigation.legal_menu.refund_policy',
};

export const menuItems: ({
  id: string;
  path: string;
  title: string;
  memberOnly?: boolean;
  hideIfLoggedIn?: boolean;
})[]= [
  { id: 'home', path: '/', title: 'Home' },
  { id: 'login', path: '/login', title: 'Login', hideIfLoggedIn: true },
  { id: 'faq', path: '/faq', title: 'FAQ' },
  ...(siteConfig.insights?.show ? [{ id: 'insights', path: '/insights', title: 'Insights' }] : []),
  { id: 'about-us', path: '/about-us', title: 'About Us' },
  { id: 'pricing', path: '/pricing', title: 'Pricing', hideIfLoggedIn: true },
  { id: 'account', path: '/user/account', title: 'My Account', memberOnly: true },
  { id: 'academy', path: '/academy', title: 'Academy', memberOnly: true },
];

export const legalItems: ({
  id: string;
  path: string;
  title: string;
  memberOnly?: boolean;
  hideIfLoggedIn?: boolean;
})[]= [
  { id: 'privacy-policy', path: 'https://blog.iqinternational.org/privacy-policy', title: 'Privacy Policy' },
  { id: 'terms-conditions', path: 'https://blog.iqinternational.org/terms-and-conditions', title: 'Terms & Conditions'},
  { id: 'cookie-policy', path: 'https://blog.iqinternational.org/cookie-policy', title: 'Cookie Policy' },
  { id: 'refund-policy', path: 'https://blog.iqinternational.org/terms-and-conditions/#:~:text=your%20own%20risk.-,6.%20Refund%20Policy,-6.1%20Trial%20Refunds', title: 'Refund Policy'},
];

export const accountAreaMenuItems = [
  { id: 'history', path: '/user/history', title: 'Test history' },
  { id: 'account', path: '/user/account', title: 'My information' },
  { id: 'change-password', path: '/user/change-password', title: 'Change my password' },
  { id: 'offer', path: '/user/subscription', title: 'My subscription' },
];
