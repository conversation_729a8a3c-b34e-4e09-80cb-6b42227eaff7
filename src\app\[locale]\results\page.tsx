'use client';

import { useEffect, useContext, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { useTranslations } from 'next-intl';
import dynamic from 'next/dynamic';
import Image from 'next/image';
import { Link } from '@/lib/i18n/navigation';
import { Loader2 } from 'lucide-react';
import { httpsCallable } from 'firebase/functions';
import { signInWithCustomToken } from 'firebase/auth';
import { sendGTMEvent } from '@next/third-parties/google';
import { functions } from '@/utils/firebase';
import { auth } from '@/utils/firebase';
import SessionContext from '@/store/SessionContext';
import ResultsButtons from '@/components/ClientComponents/ResultsButtons';
import { isGTMInitialized } from '@/utils/isGtmInitialized';
import { usePostHogAnalytics } from '@/hooks/useAnalytics';
import { PostHogEventEnum } from '@/store/types';

const DynamicResultsButtons = dynamic(() => import('@/components/ClientComponents/ResultsButtons'), {
  ssr: false,
  loading: () => <ResultsButtons {...{ sessionIdToUse: 'example', loading: true }} />,
});

const IqTestResults = () => {
  const t = useTranslations('results');
  const { captureEvent } = usePostHogAnalytics();
  const { sessionId, resetAnswers, updateQuestionId, formData, siteConfig } = useContext(SessionContext);
  const searchParams = useSearchParams();
  const sourceFromUrl = searchParams.get('source');
  const sessionIdFromUrl = searchParams.get('user');
  const sessionIdToUse = sessionIdFromUrl || sessionId;
  const [resultsReady, setResultsReady] = useState(false);

  useEffect(() => {
    const init = !!new URLSearchParams(window.location.search).get('init');
    const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));
    const reportStatus = httpsCallable(functions, 'reportStatus');
    const login = httpsCallable(functions, 'loginWithSessionId');

    async function loginWithSessionId() {
      try {
        const result: any = await login({ sessionId });
        if (result?.data?.success) {
          await signInWithCustomToken(auth, result.data.token);
        }
      } catch (err) {
        console.error('Login with session ID failed', err);
      }
    }

    async function getReportStatus() {
      for (let tries = 0; tries < 10; tries++) {
        try {
          const result: any = await reportStatus({ sessionId });
          if (result?.data?.status === 'ready') {
            if (init) await loginWithSessionId();
            setResultsReady(true);
            return;
          }
        } catch (err) {
          console.error('Error fetching report status', err);
        }
        await sleep(2000);
      }
    }

    getReportStatus();

    if (!isGTMInitialized()) {
      console.warn('GTM not initialized on results page: formSubmitted event not sent');
      return;
    }

    sendGTMEvent({
      event: 'formSubmitted',
      leadsUserData: {
        email: formData.email,
      },
    });
  }, [sessionId, formData]);

  useEffect(() => {
    if (sourceFromUrl === 'qr') return;

    resetAnswers();
    updateQuestionId(1);
  }, [sourceFromUrl]); // run only once on mount

  return (
    <div className={`flex flex-wrap justify-center`}>
      <div className={`flex flex-wrap justify-center max-w-[500px] mt-[12vh] h:mt-[12vh] mb-12`}>
        <Image
          unoptimized
          src={'./results/success.png'}
          alt={`Illustration of success. :)`}
          width={181}
          height={145}
          className="mb-10"
          priority
        />
        <div className="flex flex-col justify-center items-center" style={{}}>
          <h3 className="mb-5" style={{}}>
            {t('title')}
          </h3>
          <p className="mb-8 max-w-[90vw] xs:max-w-[380px] text-center">
            {t('thank_you_message', { siteName: siteConfig.siteName })}{' '}
            {resultsReady ? t('results_ready') : t('results_processing')}
            {!resultsReady && <Loader2 className="h-10 w-10 m-auto animate-spin mt-5 text-primary" />}
          </p>
          {resultsReady && (
            <>
              <DynamicResultsButtons {...{ sessionIdToUse, loading: false }} />
              <Link className="mt-5 text-primary underline text-lg" href="/training">
                <button
                  onClick={() => captureEvent(PostHogEventEnum.GO_TO_MEMBERS_AREA, { sessionId: sessionIdToUse })}>
                  {t('btn_members_area')}
                </button>
              </Link>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default IqTestResults;
