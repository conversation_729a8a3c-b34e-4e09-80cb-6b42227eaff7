/**
 * The single source-of-truth for every supported locale.
 * Add new items here and the whole app will immediately know about them.
 */
export enum Locale {
  En = 'en',
  // Es = 'es',
  // Fr = 'fr',
  // De = 'de',
  // Pt = 'pt',
  // Hr = 'hr',
  // El = 'el'
}

/** List all locale values, e.g. `['en', 'es', ...]` */
export const locales: string[] = Object.values(Locale);

/** The locale that will be used when no match is found. */
export const defaultLocale: Locale = Locale.En;

/** The country that will be used when no match is found. */
export const defaultCountry: string = 'US';
