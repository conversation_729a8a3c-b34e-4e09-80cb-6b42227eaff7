'use client';

import React, { memo, useContext } from 'react';
import { useTranslations } from 'next-intl';
import { accountAreaMenuItems, accountMenuTranslationKeys } from '@/app/config';
import { Link, usePathname } from '@/lib/i18n/navigation';
import { UserContext } from '@/store/UserContext';

const Menu = ({ position, className }: { position: string; className: string }) => {
  const t = useTranslations();
  const { logout } = useContext(UserContext);
  const activePath = usePathname();

  return (
    <nav className="flex flex-wrap gap-3 md:gap-10 justify-evenly md:justify-start text-black md:ml-7 mt-5 md:my-[15px] md:mt-7">
      {accountAreaMenuItems.map((item, _i) => (
        <Link
          key={item.id}
          className={`${
            item.path === activePath ? 'border-primary' : 'border-transparent'
          } transition-all duration-300 lgh:text-base xl:!text-lg border-b-2 hover:border-primary pb-1 text-black`}
          href={item.path}>
          {t(accountMenuTranslationKeys[item.id] || item.title)}
        </Link>
      ))}
      <Link className={`link  text-black lgh:text-base xl:!text-lg`} href={`#`} onClick={logout}>
        {t('navigation.account_menu.logout')}
      </Link>
      <span className="line"></span>
    </nav>
  );
};
export default memo(Menu);
