'use client';

import requireAuth from '@/components/requireAuth';
import { Link } from '@/lib/i18n/navigation';

function Trainings() {
    return (
        <div className="flex flex-col w-full items-center m-auto">
            <h3 className='my-10'>Choose training type</h3>
            <div className="flex gap-2">
                <Link className='secondary rounded-[10px]' href="/training/analytical">Analytical</Link>
                <Link className='secondary rounded-[10px]' href="/training/pattern">Pattern</Link>
                <Link className='secondary rounded-[10px]' href="/training/visual">Visual</Link>
            </div>
        </div>
    )
}

export default requireAuth(Trainings, ['is_subscribed']);