'use client';

import { useTranslations } from 'next-intl';
import requireAuth from '@/components/requireAuth';
import { Link } from '@/lib/i18n/navigation';

function Trainings() {
    const t = useTranslations('members_training');

    return (
        <div className="flex flex-col w-full items-center m-auto">
            <h3 className='my-10'>{t('title')}</h3>
            <div className="flex gap-2">
                <Link className='secondary rounded-[10px]' href="/training/analytical">{t('training_types.analytical')}</Link>
                <Link className='secondary rounded-[10px]' href="/training/pattern">{t('training_types.pattern')}</Link>
                <Link className='secondary rounded-[10px]' href="/training/visual">{t('training_types.visual')}</Link>
            </div>
        </div>
    )
}

export default requireAuth(Trainings, ['is_subscribed']);