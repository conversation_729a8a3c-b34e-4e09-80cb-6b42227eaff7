import { useState, useEffect, useContext } from 'react';
import { PaymentElement, useStripe, useElements } from '@stripe/react-stripe-js';
import '@/sass/form.scss';
import { useRouter } from '@/lib/i18n/navigation';
import '@/sass/spinner.scss';
import SessionContext from '@/store/SessionContext';
import UiContext from '@/store/UiContext';
import Image from 'next/image';
import { httpsCallable } from 'firebase/functions';
import { functions } from '@/utils/firebase';
import { Loader2 } from 'lucide-react';
import { signInWithCustomToken } from 'firebase/auth';
import { auth } from '@/utils/firebase';
import { addFormDataToSessionDb } from '@/services/session';
import { getHostUrl } from '@/utils/getHostUrl';

export default function LanguageCheckoutForm({ clientSecret, email }: { clientSecret: string; email: string }) {
  const stripe = useStripe();
  const elements = useElements();

  const [message, setMessage] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { prices, sessionId, fetchAllSessionData } = useContext(SessionContext);
  const { time } = useContext(UiContext);
  const sessionData = useContext(SessionContext);
  const compliantVersion = time < 330;
  const handleResult = httpsCallable(functions, 'handleEmotionalIntelligence');
  const emailCheck = httpsCallable(functions, 'checkIfEmailExists');
  const login = httpsCallable(functions, 'loginWithSessionId');
  useEffect(() => {
    console.log('Stripe:', stripe);
    console.log('Client Secret from URL:', clientSecret);

    if (!stripe) {
      console.log('Stripe is not initialized');
      return;
    }

    const urlClientSecret = new URLSearchParams(window.location.search).get('payment_intent_client_secret');
    console.log('URL Client Secret:', urlClientSecret);

    if (!urlClientSecret) {
      console.log('No client secret in URL');
      return;
    }
    stripe.retrievePaymentIntent(urlClientSecret).then(async ({ paymentIntent }) => {
      setIsLoading(true);
      setMessage(null);
      console.log('paymentIntent', paymentIntent);
      switch (paymentIntent?.status) {
        case 'succeeded':
          setTimeout(async () => {
            try {
              const result: any = await login({ sessionId });
              console.log('sessionid', sessionId);
              if (result.data.success) {
                signInWithCustomToken(auth, result.data.token);
                router.push('/emotional-intelligence/results');
              } else {
                await addFormDataToSessionDb({ ...fetchAllSessionData(), time, type: 'emotional-intelligence' });
                console.log('sessionid1', sessionId);
                const retryResult: any = await login({ sessionId });
                if (retryResult.data.success) {
                  signInWithCustomToken(auth, retryResult.data.token);
                  router.push('/emotional-intelligence/results');
                }
              }
            } catch (error) {
              await addFormDataToSessionDb({ ...fetchAllSessionData(), time, type: 'emotional-intelligence' });
              console.log('sessionid2', sessionId);
              const retryResult: any = await login({ sessionId });
              if (retryResult.data.success) {
                signInWithCustomToken(auth, retryResult.data.token);
                router.push('/emotional-intelligence/results');
              }
            }
          }, 3000);
          break;
        case 'processing':
          setMessage('Your payment is processing.');
          document.getElementById('paymentDetails')!.scrollIntoView();
          break;
        case 'requires_payment_method':
          setMessage('Your payment was not successful, please try again.');
          document.getElementById('paymentDetails')!.scrollIntoView();
          break;
        default:
          setMessage('Something went wrong.');
          document.getElementById('paymentDetails')!.scrollIntoView();
          break;
      }
    });
  }, [stripe, window.location.search]);

  const handleSubmit = async (e: any) => {
    e.preventDefault();

    if (!stripe || !elements) {
      // Stripe.js hasn't yet loaded.
      // Make sure to disable form submission until Stripe.js has loaded.
      return;
    }

    setIsLoading(true);

    const { error } = await stripe.confirmPayment({
      elements,
      confirmParams: {
        // Make sure to change this to your payment completion page
        return_url: `${getHostUrl()}/emotional-intelligence/payment`,
        //return_url: `${
        //process.env.NEXT_PUBLIC_HOST_URL ?? `https://${process.env.NEXT_PUBLIC_VERCEL_URL}`
        //}/emotional-intelligence/payment`,
      },
    });

    // This point will only be reached if there is an immediate error when
    // confirming the payment. Otherwise, your customer will be redirected to
    // your `return_url`. For some payment methods like iDEAL, your customer will
    // be redirected to an intermediate site first to authorize the payment, then
    // redirected to the `return_url`.
    if (error.type === 'card_error' || error.type === 'validation_error') {
      setMessage(error.message ?? null);
    } else {
      setMessage('An unexpected error occurred.');
    }

    setIsLoading(false);
  };

  return (
    <>
      {/* Title & Security */}
      <div className="w-full flex flex-row justify-between items-center mb-5 md:mb-6">
        <span className="font-raleway font-bold text-[20px] md:text-[26px] leading-[24px] md:leading-[34px] text-[#0E2432] tracking-normal md:tracking-tight">
          Payment Details
        </span>
        <div className="flex flex-row gap-[8px] bg-[#F6F6F6] py-[8px] px-[10px] md:px-[16px] rounded-[8px]">
          <Image
            src="/images/emotional-intelligence/payment/card.svg"
            alt="shield keyhole"
            width={18}
            height={18}
            className="w-[16px] h-auto md:w-[18px]"
          />
          <span
            className="flex items-center text-[14px] md:text-[16px] leading-[18px] md:leading-[20px] font-raleway font-semibold text-[#0E2432]"
            style={{ fontFeatureSettings: "'pnum' on, 'lnum' on" }}>
            100% security
          </span>
        </div>
      </div>
      <form onSubmit={handleSubmit}>
        {message && <div className="mb-5 mt-2 text-red-500 font-bold text-lg">{message}</div>}
        <PaymentElement />
        <button
          disabled={isLoading || !stripe || !elements}
          id="submit"
          className="flex items-center justify-center text-white w-full button bg-[#8C36D0] justify-center mt-3 rounded-[10px] md:p-5 p-3 md:min-h-[82px] min-h-[60px]">
          <span className="font-bold text-base md:text-xl" id="button-text">
            {isLoading ? (
              <Loader2 className="animate-spin" />
            ) : compliantVersion ? (
              'Get My Full EQ Report Now!'
            ) : (
              'Get My Full EQ Report Now!'
            )}
          </span>
        </button>
        <span
          className="inline-block mt-4 font-raleway font-normal text-[12px] leading-[18px] md:leading-[20px] text-[#8893AC] tracking-[-0.02em]"
          style={{ fontFeatureSettings: "'pnum' on, 'lnum' on" }}>
          By clicking Get My Full EQ Report Now above you agree to be charged {prices.oneTime.formatted} (which includes
          your complete Love Report, an additional quiz and report, and access to a 28-day Personal Growth Challenge).
          You also agree to our Terms of Use and Privacy Policy. After 7-days you will be billed{' '}
          {prices.subscription.formatted} every 4 weeks until your subscription ends. Cancel anytime.
        </span>
      </form>
    </>
  );
}
