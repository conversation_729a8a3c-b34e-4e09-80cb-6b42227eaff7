/** @type {import('next').NextConfig} */
import withBundleAnalyzer from '@next/bundle-analyzer';
import createNextIntlPlugin from 'next-intl/plugin';
import { getSiteConfig } from './site.config.js';
const siteConfig = getSiteConfig();

const nextConfig = {
  reactStrictMode: false,
  trailingSlash: false,
  //output: 'export',
  //https://celeste.page/users/AhTtapWyU7P64ZkIqT6Nn39hhio1/articles/ZCPQfJ3tAuFDtl2npaug
  //https://community.spiceworks.com/t/redirecting-a-subfolder-to-another-server/568106/8
  //https://posthog.com/docs/advanced/proxy/nextjs
  async rewrites() {
    return {
      fallback: [
        {
          source: '/insights',
          destination: `${siteConfig.insights.url}`,
        },
        {
          source: '/insights/:path*',
          destination: `${siteConfig.insights.url}/:path*/`,
        },
      ],
    };
  },
  images: {
    formats: ['image/avif', 'image/webp'],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: siteConfig.domain,
        port: '',
        pathname: '/**/**',
      },
      {
        protocol: 'https',
        hostname: 'firebasestorage.googleapis.com',
        port: '',
        pathname: '/**/**',
      },
    ],
  },
  sassOptions: {
    includePaths: ['./src/sass'],
  },
};

// --- plugin setup -----------------------------------------------------------
const withNextIntl = createNextIntlPlugin('./src/lib/i18n/request.ts');

// build the final config
export default process.env.ANALYZE === 'true' ? withBundleAnalyzer(withNextIntl(nextConfig)) : withNextIntl(nextConfig);

/*
export default withSentryConfig(nextConfig, {
// For all available options, see:
// https://github.com/getsentry/sentry-webpack-plugin#options

// Suppresses source map uploading logs during build
silent: true,
org: "robust-digital-kft",
project: "javascript-nextjs",
}, {
// For all available options, see:
// https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/

// Upload a larger set of source maps for prettier stack traces (increases build time)
widenClientFileUpload: true,

// Transpiles SDK to be compatible with IE11 (increases bundle size)
transpileClientSDK: true,

// Route browser requests to Sentry through a Next.js rewrite to circumvent ad-blockers.
// This can increase your server load as well as your hosting bill.
// Note: Check that the configured route will not match with your Next.js middleware, otherwise reporting of client-
// side errors will fail.
tunnelRoute: "/monitoring",

// Hides source maps from generated client bundles
hideSourceMaps: true,

// Automatically tree-shake Sentry logger statements to reduce bundle size
disableLogger: true,

// Enables automatic instrumentation of Vercel Cron Monitors.
// See the following for more information:
// https://docs.sentry.io/product/crons/
// https://vercel.com/docs/cron-jobs
automaticVercelMonitors: true,
});
*/

/*const nextConfig = withAxiom({
  //output: 'export',
  async rewrites() {
    return {
      fallback: [
        {
          source: '/insights/:path*',
          destination: `https://blog.iqinternational.org/:path*`,
        },
      ],
    };
  },
  images: {
    formats: ['image/avif', 'image/webp'],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'iqinternational.org',
        port: '',
        pathname: '/*
      /*},
    ],
  },
  sassOptions: {
    includePaths: ['./src/sass'],
  },
  /*compiler: {
    removeConsole: true,
  },
});*/
