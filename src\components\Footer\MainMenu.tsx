'use client';

import { useContext } from 'react';
import { Link as I18nLink, usePathname } from '@/lib/i18n/navigation';
import NextLink from 'next/link';
import { useTranslations } from 'next-intl';
import { menuItems, menuItemTranslationKeys } from '@/app/config';
import { UserContext } from '@/store/UserContext';

const MainMenu = () => {
  const t = useTranslations('footer');
  const tNav = useTranslations();
  const activePath = usePathname();
  const { user } = useContext(UserContext);

  return (
    <div className="flex flex-col gap-[16px]">
      <span className="text-[18px] leading-[27px]">{t('sections.menu.title')}</span>
      <div className="flex flex-col gap-[8px] text-[16px] leading-[24px]">
        {menuItems
          .filter(x =>
            !(activePath == '/results' && x.id == 'pricing') && !user ? !x.memberOnly : !x.hideIfLoggedIn
          )
          .map(item => {
            const { id, path, title } = item;
            // Use regular Link for insights to bypass internationalization
            const Link = id === 'insights' ? NextLink : I18nLink;
            return (
              <Link key={id} href={`${path}`}>
                {tNav(menuItemTranslationKeys[id] || title)}
              </Link>
            );
          })}
      </div>
    </div>
  );
};

export default MainMenu;
