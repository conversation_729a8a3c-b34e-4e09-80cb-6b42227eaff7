'use client';

import { useContext } from 'react';
import { Link as I18nLink, usePathname } from '@/lib/i18n/navigation';
import NextLink from 'next/link';
import { useTranslations } from 'next-intl';
import { menuItems, menuItemTranslationKeys } from '@/app/config';
import { UserContext } from '@/store/UserContext';
import { shouldShowMenuItem, getLinkType, getMenuItemText } from '@/utils/menuUtils';

const MainMenu = () => {
  const t = useTranslations('footer');
  const tNav = useTranslations();
  const activePath = usePathname();
  const { user } = useContext(UserContext);

  return (
    <div className="flex flex-col gap-[16px]">
      <span className="text-[18px] leading-[27px]">{t('sections.menu.title')}</span>
      <div className="flex flex-col gap-[8px] text-[16px] leading-[24px]">
        {menuItems
          .filter(item => shouldShowMenuItem(item, activePath, user))
          .map(item => {
            const { id, path, title } = item;
            const linkType = getLinkType(id, path);
            const Link = linkType === 'regular' ? NextLink : I18nLink;
            const text = getMenuItemText(id, title, menuItemTranslationKeys, tNav);

            return (
              <Link key={id} href={path}>
                {text}
              </Link>
            );
          })}
      </div>
    </div>
  );
};

export default MainMenu;
