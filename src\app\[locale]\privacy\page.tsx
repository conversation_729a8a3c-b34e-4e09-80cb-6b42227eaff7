"use client";
import { snakeCase } from 'lodash';
import { Link } from '@/lib/i18n/navigation';
import { useContext, useMemo } from 'react';
import SessionContext from '@/store/SessionContext';

const AboutUs = () => {
  const { siteConfig } = useContext(SessionContext);
  const data = useMemo(
    () => [
      {
        title: 'Introduction',
        text: [
          `This Privacy Policy describes the policies of ${siteConfig.companyName}, ${siteConfig.companyAddress}, email: ${siteConfig.supportEmail}, phone: ${siteConfig.companyPhone} on the collection, use and disclosure of your information that we collect when you use our website ( https://${siteConfig.domain} ). (the “Service”). By accessing or using the Service, you are consenting to the collection, use and disclosure of your information in accordance with this Privacy Policy. If you do not consent to the same, please do not access or use the Service.`,
          `We may modify this Privacy Policy at any time without any prior notice to you and will post the revised Privacy Policy on the Service. The revised Policy will be effective 180 days from when the revised Policy is posted in the Service and your continued access or use of the Service after such time will constitute your acceptance of the revised Privacy Policy. We therefore recommend that you periodically review this page.\n
          Last Updated On 18-Apr-2024
          Effective Date 18-Apr-2024`,
        ],
      },
      {
        title: 'Consent',
        text: ['By using our website, you hereby consent to our Privacy Policy and agree to its terms.'],
      },
      {
        title: 'Information we collect',
        text: [
          'The personal information that you are asked to provide, and the reasons why you are asked to provide it, will be made clear to you at the point we ask you to provide your personal information.',
          'We will collect and process the following personal information about you: Email.', //Name, Email, Age.
        ],
      },
      {
        title: 'How We Use Your Information',
        text: [
          'We will use the information that we collect about you for the following purposes:',
          [
            'Marketing/ Promotional',
            'Processing payment',
            'Administration info',
            'Manage customer order',
            'Dispute resolution',
            'Anonym insights data on usage, to improve our service',
          ],
        ],
      },
      {
        title: 'Log Files',
        text: [
          `${siteConfig.siteName} Organization follows a standard procedure of using log files. These files log visitors when they visit websites. All hosting companies do this and a part of hosting services’ analytics. The information collected by log files include internet protocol (IP) addresses, browser type, Internet Service Provider (ISP), date and time stamp, referring/exit pages, and possibly the number of clicks. These are not linked to any information that is personally identifiable. The purpose of the information is for analyzing trends, administering the site, tracking users’ movement on the website, and gathering demographic information. Our Privacy Policy was created with the help of the Privacy Policy Generator and the Online Privacy Policy Generator.`,
        ],
      },
      {
        title: 'Cookies and Web Beacons',
        text: [
          `Like any other website, ${siteConfig.siteName} Organization uses ’cookies’. These cookies are used to store information including visitors’ preferences, and the pages on the website that the visitor accessed or visited. The information is used to optimize the users’ experience by customizing our web page content based on visitors’ browser type and/or other information.`,
          `To learn more about how we use these and your choices in relation to these tracking technologies, please refer to our `,
        ],
      },
      /*{
        title: 'Advertising Partners Privacy Policies',
        text: [
          `You may consult this list to find the Privacy Policy for each of the advertising partners of ${siteConfig.siteName} Organization.`,
          `Third-party ad servers or ad networks uses technologies like cookies, JavaScript, or Web Beacons that are used in their respective advertisements and links that appear on ${siteConfig.siteName} Organization, which are sent directly to users’ browser. They automatically receive your IP address when this occurs. These technologies are used to measure the effectiveness of their advertising campaigns and/or to personalize the advertising content that you see on websites that you visit.`,
          `Note that ${siteConfig.siteName} Organization has no access to or control over these cookies that are used by third-party advertisers.`,
        ],
      },*/
      {
        title: 'Third Party Privacy Policies',
        text: [
          'We will not transfer your personal information to any third party without seeking your consent, except in limited circumstances as Analytics and Usage information.',
          'We require such third party’s to use the personal information we transfer to them only for the purpose for which it was transferred and not to retain it for longer than is required for fulfilling the said purpose.',
          'We may also disclose your personal information for the following: (1) to comply with applicable law, regulation, court order or other legal process; (2) to enforce your agreements with us, including this Privacy Policy; or (3) to respond to claims that your use of the Service violates any third-party rights. If the Service or our company is merged or acquired with another company, your information will be one of the assets that is transferred to the new owner.',
        ],
      },
      {
        title: 'CCPA Privacy Rights (Do Not Sell My Personal Information)',
        text: [
          'Under the CCPA, among other rights, California consumers have the right to:',
          'Request that a business that collects a consumer’s personal data disclose the categories and specific pieces of personal data that a business has collected about consumers.',
          'Request that a business delete any personal data about the consumer that a business has collected.',
          'Request that a business that sells a consumer’s personal data, not sell the consumer’s personal data.',
          'If you make a request, we have one month to respond to you. If you would like to exercise any of these rights, please contact us.',
        ],
      },
      {
        title: 'GDPR Data Protection Rights',
        text: [
          `Depending on the law that applies, you may have a right to access and rectify or erase your personal data or receive a copy of your personal data, restrict or object to the active processing of your data, ask us to share (port) your personal information to another entity, withdraw any consent you provided to us to process your data, a right to lodge a complaint with a statutory authority and such other rights as may be relevant under applicable laws. To exercise these rights, you can write to us at ${siteConfig.supportEmail}. We will respond to your request in accordance with applicable law.`,
          `You may opt-out of direct marketing communications or the profiling we carry out for marketing purposes by writing to us at ${siteConfig.supportEmail}.`,
          'Do note that if you do not allow us to collect or process the required personal information or withdraw the consent to process the same for the required purposes, you may not be able to access or use the services for which your information was sought.',
        ],
      },
      {
        title: 'Retention of Your Information',
        text: [
          'We will retain your personal information with us for 12 month or for as long as we need it to fulfill the purposes for which it was collected as detailed in this Privacy Policy. We may need to retain certain information for longer periods such as record-keeping / reporting in accordance with applicable law or for other legitimate reasons like enforcement of legal rights, fraud prevention, etc. Residual anonymous information and aggregate information, neither of which identifies you (directly or indirectly), may be stored indefinitely.',
        ],
      },
      {
        title: 'Security',
        text: [
          'The security of your information is important to us and we will use reasonable security measures to prevent the loss, misuse or unauthorized alteration of your information under our control. However, given the inherent risks, we cannot guarantee absolute security and consequently, we cannot ensure or warrant the security of any information you transmit to us and you do so at your own risk.',
        ],
      },
      {
        title: 'Grievance / Data Protection Officer',
        text: [
          `If you have any queries or concerns about the processing of your information that is available with us, you may email our Grievance Officer at ${siteConfig.companyName}, ${siteConfig.companyAddress}, email: ${siteConfig.supportEmail}. We will address your concerns in accordance with applicable law.`,
        ],
      },
      {
        title: 'Children’s Information',
        text: [
          'Another part of our priority is adding protection for children while using the internet. We encourage parents and guardians to observe, participate in, and/or monitor and guide their online activity.',
          `${siteConfig.siteName} Organization does not knowingly collect any Personal Identifiable Information from children under the age of 13. If you think that your child provided this kind of information on our website, we strongly encourage you to contact us immediately and we will do our best efforts to promptly remove such information from our records.`,
        ],
      },
    ],
    []
  );

  return (
    <div>
      <div className="w-full h-[100px] sm:h-[130px]"></div>
      <section id="privacy" className={`flex flex-wrap justify-between max-w-[1440px] m-auto mt-5 pt-0`} style={{}}>
        <div className="flex flex-wrap flex-col max-w-[416px] w-full lg:w-[40%]">
          <div style={{}}>
            <h1 className="mb-10 text-5xl" style={{ maxWidth: 353 }}>
              Privacy Policy
            </h1>
          </div>
          <ol style={{ color: '#191919', listStyleType: 'decimal', paddingLeft: 20 }}>
            {data.map((item, i) => (
              <a key={i} href={`#${snakeCase(item.title)}`}>
                <li style={{ paddingLeft: 6, marginBottom: 12 }}>{`${item.title}`}</li>
              </a>
            ))}
          </ol>
        </div>
        <div className="max-w-[736px]  w-full lg:w-[60%]" style={{}}>
          {data.map((item, index) => (
            <div key={index}>
              <h3
                className="big"
                id={snakeCase(item.title)}
                style={{ marginBottom: 24, marginTop: index > 0 ? 48 : 0, scrollMarginTop: 150 }}>
                {item.title}
              </h3>
              {item.text.map((paragraph, i) =>
                paragraph.constructor === Array ? (
                  <div key={i}>
                    <ul style={{ listStyleType: 'disc', paddingLeft: 40 }}>
                      {paragraph.map((li, i) => (
                        <li key={i}>{li}</li>
                      ))}
                    </ul>
                  </div>
                ) : (
                  <p key={i} style={{ marginBottom: 16, whiteSpace: 'pre-line' }}>
                    {paragraph}
                    {item.title.includes('Cookies') && i === 1 && (
                      <span>
                        <Link className='hover:underline text-primary mb-4' target='_blank' href='/cookies'>
                          Cookie Policy
                        </Link>
                        {'.'}
                      </span>
                    )}
                  </p>
                )
              )}
            </div>
          ))}
        </div>
      </section>
    </div>
  );
};

export default AboutUs;
