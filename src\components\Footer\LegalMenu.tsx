'use client';

import { useContext } from 'react';
import { Link as I18nLink, usePathname } from '@/lib/i18n/navigation';
import NextLink from 'next/link';
import { useTranslations } from 'next-intl';
import { legalItems, legalMenuTranslationKeys } from '@/app/config';
import { UserContext } from '@/store/UserContext';

const LegalMenu = () => {
  const t = useTranslations('footer');
  const tNav = useTranslations();
  const activePath = usePathname();
  const { user } = useContext(UserContext);

  return (
    <div className="flex flex-col gap-[16px]">
      <span className="text-[18px] leading-[27px]">{t('sections.legal.title')}</span>
      <div className="flex flex-col gap-[8px] text-[16px] leading-[24px]">
        {legalItems
          .filter(x =>
            !(activePath == '/results' && x.id == 'pricing') && !user ? !x.memberOnly : !x.hideIfLoggedIn
          )
          .map(item => {
            const { id, path, title } = item;
            // Use regular Link for external URLs
            const Link = path.startsWith('http') ? NextLink : I18nLink;
            return (
              <Link key={id} href={`${path}`}>
                {tNav(legalMenuTranslationKeys[id] || title)}
              </Link>
            );
          })}
      </div>
    </div>
  );
};

export default LegalMenu;
