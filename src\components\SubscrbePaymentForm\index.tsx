'use client';

import { useState, useEffect, useContext } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import { Elements, PaymentElement, useElements, useStripe } from '@stripe/react-stripe-js';
import { useLocale } from 'next-intl';
import SessionContext from '@/store/SessionContext';
import { getHostUrl } from '@/utils/getHostUrl';

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

export default function SubscribePaymentForm({ clientSecret }: { clientSecret: string }) {
  return (
    <div id="paymentDetails" className="flex justify-center w-[500px] max-w-[90%]">
      {clientSecret && (
        <div className="flex flex-col grow">
          <div className="mx-5">
            <Elements options={{ clientSecret, appearance: { theme: 'stripe' } }} stripe={stripePromise}>
              <CheckoutForm />
            </Elements>
          </div>
        </div>
      )}
    </div>
  );
}

function CheckoutForm() {
  const stripe = useStripe();
  const elements = useElements();
  const locale = useLocale();

  const [message, setMessage] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { prices } = useContext(SessionContext);

  useEffect(() => {
    if (!stripe) {
      return;
    }

    const clientSecret = new URLSearchParams(window.location.search).get('payment_intent_client_secret');

    if (!clientSecret) {
      return;
    }

    stripe.retrievePaymentIntent(clientSecret).then(({ paymentIntent }) => {
      switch (paymentIntent?.status) {
        case 'succeeded':
          window.location.href = `${getHostUrl()}/${locale}/user/subscription?success=true`;
          //window.location = `${process.env.NEXT_PUBLIC_HOST_URL ?? `https://${process.env.NEXT_PUBLIC_VERCEL_URL}`}/${locale}/user/subscription?success=true`;
          break;
        case 'processing':
          setMessage('Your payment is processing.');
          document.getElementById('paymentDetails')!.scrollIntoView();
          break;
        case 'requires_payment_method':
          setMessage('Your payment was not successful, please try again.');
          document.getElementById('paymentDetails')!.scrollIntoView();
          break;
        default:
          setMessage('Something went wrong.');
          document.getElementById('paymentDetails')!.scrollIntoView();
          break;
      }
    });
  }, [stripe, locale]);

  const handleSubmit = async (e: any) => {
    e.preventDefault();

    if (!stripe || !elements) {
      // Stripe.js hasn't yet loaded.
      // Make sure to disable form submission until Stripe.js has loaded.
      return;
    }

    setIsLoading(true);

    const { error } = await stripe.confirmPayment({
      elements,
      confirmParams: {
        return_url: `${getHostUrl()}/${locale}/user/subscription`,
        //return_url: `${process.env.NEXT_PUBLIC_HOST_URL ?? `https://${process.env.NEXT_PUBLIC_VERCEL_URL}`}/${locale}/user/subscription`,
      },
    });

    // This point will only be reached if there is an immediate error when
    // confirming the payment. Otherwise, your customer will be redirected to
    // your `return_url`. For some payment methods like iDEAL, your customer will
    // be redirected to an intermediate site first to authorize the payment, then
    // redirected to the `return_url`.
    if (error.type === 'card_error' || error.type === 'validation_error') {
      setMessage(error.message ?? null);
    } else {
      setMessage('An unexpected error occurred.');
    }

    setIsLoading(false);
  };

  return (
    <form onSubmit={handleSubmit}>
      {message && <div className="mb-5 mt-2 text-red-500 text-center font-bold text-lg">{message}</div>}
      <div className="flex justify-between bg-gray-100 rounded-md p-5  mb-4">
        <h5 className="items-center">Total</h5>
        <h5>
          {prices.subscription.formatted}
          {prices.vatIncluded ? ' (incl. VAT)' : ''}
        </h5>
      </div>
      <PaymentElement />
      <button
        disabled={isLoading || !stripe || !elements}
        id="submit"
        className="flex w-full button primary text-base md:text-xl font-semibold justify-center mt-3 rounded-[10px] leading-">
        <span id="button-text">{isLoading ? <div className="spinner" id="spinner"></div> : 'Subscribe'}</span>
      </button>
    </form>
  );
}
