'use client';

import { useState, useEffect, useContext, useRef } from 'react';
import { PaymentElement, useStripe, useElements } from '@stripe/react-stripe-js';
import { useRouter } from '@/lib/i18n/navigation';
import { sendGTMEvent } from '@next/third-parties/google';
import SessionContext from '@/store/SessionContext';
import UiContext from '@/store/UiContext';
import { getHostUrl } from '@/utils/getHostUrl';
import { isGTMInitialized } from '@/utils/isGtmInitialized';
import '@/sass/form.scss';
import '@/sass/spinner.scss';
import { usePostHogAnalytics } from '@/hooks/useAnalytics';
import { PostHogEventEnum } from '@/store/types';

export default function CheckoutForm() {
  const stripe = useStripe();
  const elements = useElements();
  const hasFiredGTM = useRef(false);
  const { captureEvent } = usePostHogAnalytics();

  const { checkoutId, prices, getIsTrial } = useContext(SessionContext);
  const [message, setMessage] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { time } = useContext(UiContext);

  const compliantVersion = time < 330;

  useEffect(() => {
    if (!stripe) return;

    const clientSecret = new URLSearchParams(window.location.search).get('payment_intent_client_secret');
    if (!clientSecret) return;

    /**
     * Sends a Google Tag Manager (GTM) purchase event with eCommerce details.
     *
     * This function pushes a 'purchase' event to the GTM dataLayer,
     * including transaction details like currency, amount, transaction ID,
     * and purchased item information. It ensures GTM is initialized before sending.
     *
     * @param prices - An object containing pricing details (expects `currency` and `oneTime.amount`).
     * @param checkoutId - A unique identifier for the purchase transaction.
     */
    const trackGTMPurchaseEvent = (
      prices: { currency: string; oneTime: { amount: number } },
      checkoutId: string
    ): void => {
      if (hasFiredGTM.current) return;

      if (!isGTMInitialized()) {
        console.warn('GTM not initialized: purchase event not sent');
        return;
      }

      sendGTMEvent({
        event: 'purchase',
        ecommerce: {
          currency: prices.currency.toUpperCase(),
          value: prices.oneTime.amount,
          transaction_id: checkoutId,
          items: [
            {
              item_name: 'IQ test',
              item_id: 'iqtest',
              price: prices.oneTime.amount,
              quantity: 1,
            },
          ],
        },
      });

      hasFiredGTM.current = true;
    };

    const scrollToDetails = () => {
      document.getElementById('paymentDetails')?.scrollIntoView();
    };

    stripe.retrievePaymentIntent(clientSecret).then(({ paymentIntent }) => {
      switch (paymentIntent?.status) {
        case 'succeeded':
          trackGTMPurchaseEvent(prices, checkoutId);
          captureEvent(PostHogEventEnum.PAYMENT_SUCCESS, { paymentSystem: 'stripe', ...paymentIntent });
          router.push('/results?init=1');
          break;

        case 'processing':
          setMessage('Your payment is processing.');
          scrollToDetails();
          break;

        case 'requires_payment_method':
          setMessage('Your payment was not successful, please try again.');
          scrollToDetails();
          break;

        default:
          setMessage('Something went wrong.');
          scrollToDetails();
          break;
      }
    });
  }, [stripe, checkoutId, prices, router]);

  const handleSubmit = async (e: any) => {
    captureEvent(PostHogEventEnum.CHECKOUT_CONFIRMED, { paymentSystem: 'stripe' });
    e.preventDefault();

    if (!stripe || !elements) {
      // Stripe.js hasn't yet loaded.
      // Make sure to disable form submission until Stripe.js has loaded.
      return;
    }

    setIsLoading(true);

    const { error } = await stripe.confirmPayment({
      elements,
      confirmParams: {
        // Make sure to change this to your payment completion page
        return_url: `${getHostUrl()}/checkout`,
        //return_url: `${process.env.NEXT_PUBLIC_HOST_URL ?? `https://${process.env.NEXT_PUBLIC_VERCEL_URL}`}/checkout`,
      },
    });

    // This point will only be reached if there is an immediate error when
    // confirming the payment. Otherwise, your customer will be redirected to
    // your `return_url`. For some payment methods like iDEAL, your customer will
    // be redirected to an intermediate site first to authorize the payment, then
    // redirected to the `return_url`.
    if (error.type === 'card_error' || error.type === 'validation_error') {
      setMessage(error.message ?? null);
    } else {
      setMessage('An unexpected error occurred.');
    }

    setIsLoading(false);
  };

  return (
    <form onSubmit={handleSubmit}>
      {message && <div className="mb-5 mt-2 text-red-500 font-bold text-lg">{message}</div>}
      <div className="flex justify-between bg-grey-bg p-5 mx-4 md:mx-0 mb-4">
        <h5 className="items-center">Total</h5>
        {getIsTrial() ? <h5>{prices.oneTime.formatted}</h5> : <h5>{prices.subscription.formatted}</h5>}
      </div>
      {compliantVersion && getIsTrial() && (
        <p className="text-sm mb-6">
          Price of {prices.oneTime.formatted}
          {prices.vatIncluded ? ' (incl. VAT)' : ''} for a 2-day trial. At the end of the trial period and without
          cancellation from your side, our offer will be automatically renewed as a non-binding subscription, at the
          price of {prices.subscription.formatted} per month{prices.vatIncluded ? ' (incl. VAT)' : ''}.
        </p>
      )}
      <PaymentElement />
      <button
        disabled={isLoading || !stripe || !elements}
        id="submit"
        className="flex w-full button primary text-base md:text-xl font-semibold justify-center mt-3 rounded-[10px] leading-">
        <span id="button-text">
          {isLoading ? (
            <div className="spinner" id="spinner"></div>
          ) : compliantVersion && getIsTrial() ? (
            'Start trial and Get IQ results'
          ) : (
            'Get IQ Results'
          )}
        </span>
      </button>
    </form>
  );
}
