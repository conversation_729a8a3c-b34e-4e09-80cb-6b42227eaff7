'use client';
import { useState, useContext } from 'react';
import { useTranslations } from 'next-intl';
import { UserContext } from '@/store/UserContext';
import { Loader2 } from 'lucide-react';
import { auth } from '@/utils/firebase';
import { updateProfile } from 'firebase/auth';
import type { User } from 'firebase/auth';
import requireAuth from '@/components/requireAuth';

const Account = () => {
  const t = useTranslations('members_account');
  const { user } = useContext(UserContext);
  const { email, displayName } = user ?? {};
  const [name, setName] = useState(displayName ?? "");
  const [message, setMessage] = useState('');
  const [processing, setProcessing] = useState<boolean>(false);
  const [success, setSuccess] = useState(false);

  async function changeName() {
    setSuccess(false);
    setProcessing(true);
    if (name.length === 0) {
      setMessage(t('messages.name_empty'));
      setProcessing(false);
      return;
    }
    setMessage('');

    try {
      await updateProfile(auth.currentUser as User, { displayName: name });
      await auth.currentUser?.getIdToken(true);
      setSuccess(true);
    } catch (e) {
      setMessage(t('messages.update_error'));
    } finally {
      setProcessing(false);
    }
  }

  return (
    <div id='msform' onSubmit={(e) => e.preventDefault()}>
      <h1
        className='fs-title mb-5 text-2xl'
        style={{
          fontSize: 32,
          lineHeight: '112.5%',
        }}
      >
        {t('title')}
      </h1>
        {message && <div className='mb-5'>{message}</div>}
        {success && <div className='mb-5 font-bold text-primary'>{t('messages.update_success')}</div>}
        <input type='text' name='name' placeholder={t('name_placeholder')} onChange={e => setName(e.target.value)} value={name} />
        <input type='text' name='email' placeholder={t('email_placeholder')} disabled value={email ?? ''} />
        <button disabled={processing} onClick={changeName} className='action-button flex justify-center' style={{ marginTop: 4 }}>
          {processing ? <Loader2 className='h-4 w-4 animate-spin m-[5px] mr-2' /> : ''}
          {t('btn_submit')}
        </button>
    </div>
  );
};

export default requireAuth(Account, []);
