import { correctAnswers, categories } from './correctAnswers';

import { countBy, entries, flow, head, keys, last, maxBy, partialRight } from 'lodash';

import type { Answer } from '@/store/types';

export const calculateBestCategory = ({ answers }: { answers: Answer[] }) => {
  const correctCategories: string[] = [];

  if (answers !== undefined) {
    answers.forEach((answer) => {
      const { questionId, answerId } = answer;
      const found = correctAnswers.find((ca) => ca.questionId === questionId);
      if (answerId === found?.correctAnswerId) correctCategories.push(found.category);
    });
  }

  const bestCategory = flow(
    countBy,
    entries,
    partialRight(maxBy, last),
    head,
  )(correctCategories) as keyof typeof categories;

  //https://stackoverflow.com/questions/49731282/the-most-frequent-item-of-an-array-using-lodash
  return categories[bestCategory];
};

