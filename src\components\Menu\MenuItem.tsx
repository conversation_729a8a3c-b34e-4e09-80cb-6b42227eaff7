//https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#link-component

'use client';

import { Link, usePathname } from '@/lib/i18n/navigation';
import useLayoutStore from '@/store/useLayoutStore';

const MenuItem = ({ item: { path, title, memberOnly } }: { item: { id: string; path: string; title: string, memberOnly?: boolean } }) => {
  const activePath = usePathname();
  const { handleMobileOpen } = useLayoutStore();

  return (
    <>
      <Link
        className={`${path === activePath ? 'border-primary' : 'border-transparent'} transition-all duration-300 lgh:text-base xl:!text-lg border-b-2 hover:border-primary pb-1 font-semibold  ${memberOnly ? 'text-primary' : 'text-black'}`}
        href={`${path}`}
        onClick={() => handleMobileOpen(false)}
        prefetch={path.includes('insights') ? false : true}
      >
        {title}
      </Link>
    </>
  );
};
export default MenuItem;
