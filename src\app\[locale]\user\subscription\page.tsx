'use client';

// Styles
import 'react-responsive-modal/styles.css';
import { useContext, useEffect, useState } from 'react';
import dayjs from 'dayjs';
import localizedFormat from 'dayjs/plugin/localizedFormat';
import { httpsCallable } from 'firebase/functions';
import { Loader2 } from 'lucide-react';
import { Modal } from 'react-responsive-modal';
import { useRouter } from '@/lib/i18n/navigation';
import { auth, functions } from '@/utils/firebase';
import SubscribePaymentForm from '@/components/SubscrbePaymentForm';
import requireAuth from '@/components/requireAuth';
import { UserContext } from '@/store/UserContext';
import SessionContext from '@/store/SessionContext';

dayjs.extend(localizedFormat);

function Subscription() {
  const successfulSubscription = new URLSearchParams(window.location.search).get('success');
  const { paymentSystem } = useContext(SessionContext);  const router = useRouter();

  const { user } = useContext(UserContext);
  const { updateStripeInvoices, updateSolidgateInvoices } = useContext(SessionContext);
  const [modalOpen, setModalOpen] = useState(false);
  const [processingUpdate, setProcessingUpdate] = useState(false);
  const [showPaymentForm, setShowPaymentForm] = useState(false);
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [loadingCustomerPortal, setLoadingCustomerPortal] = useState(false);

  async function cancelSubscription() {
    let cancelFunction: string;

    const cancel = httpsCallable(
      functions,
      user?.activeSubscriptionType === 'solidgate' ? 'cancelSubscriptionSolidgate' : 'cancelSubscription'
    );
    const result = await cancel();
    const refreshResult = await auth.currentUser?.getIdToken(true);
    setProcessingUpdate(false);
  }

  async function resumeSubscription() {
    setProcessingUpdate(true);
    const resume = httpsCallable(
      functions,
      user?.activeSubscriptionType === 'solidgate' ? 'resumeSubscriptionSolidgate' : 'resumeSubscription'
    );
    const result = await resume();
    const refreshResult = await auth.currentUser?.getIdToken(true);
    setProcessingUpdate(false);
  }

  async function subscribe() {
    setProcessingUpdate(true);
    const create = httpsCallable(functions, 'createSubscription');
    const result: any = await create();
    if (!result.data.success) {
      setClientSecret(result.data.clientSecret);
      setShowPaymentForm(true);
    } else {
      window.location = '/user/subscription?success=true' as any;
    }

    setProcessingUpdate(false);
  }

  async function goToSelfServicePortal() {
    if (loadingCustomerPortal) return;
    setLoadingCustomerPortal(true);
    const createPortalSession = httpsCallable(functions, 'createPortalSession');
    const result: any = await createPortalSession();
    window.location = result.data.url;
  }

  const goToInvoices = async () => {
    const isSolidgate = process.env.NEXT_PUBLIC_USE_PAYMENT === 'solidgate';
    const getInvoices = httpsCallable(functions, isSolidgate ? 'getSolidgateInvoicesInfo' : 'getStripeInvoicesInfo');
    const invoices: any = await getInvoices({ email: user?.email });
    isSolidgate ? updateSolidgateInvoices(invoices) : updateStripeInvoices(invoices);
    router.push('/invoices');
  };

  useEffect(() => {
    auth.currentUser?.getIdToken(true);
  }, [user]);

  useEffect(() => {
    const clientSecret = new URLSearchParams(window.location.search).get('payment_intent_client_secret');
    if (clientSecret) {
      setClientSecret(clientSecret);
      setShowPaymentForm(true);
    }
  }, []);

  return (
    <div className="flex flex-col items-center mt-10">
      <h1 className="fs-title mb-2 text-[32px] leading-[112.5%]">My subscription</h1>

      <div className="mt-1 border-b w-full mb-10 flex justify-center md:justify-end pb-2">
        <div className="flex flex-col items-end">
          {paymentSystem !== 'solidgate' && (
            <button
              onClick={goToSelfServicePortal}
              className="text-primary hover:opacity-90 font-semibold text-sm flex gap-1">
              {loadingCustomerPortal && <Loader2 className="h-4 w-4 animate-spin relative top-[1px]" />}
              <span>Manage payment methods</span>
            </button>
          )}
          <button onClick={goToInvoices} className="text-primary hover:opacity-90 inline-flex font-semibold text-sm">
            {loadingCustomerPortal && <Loader2 className="h-4 w-4 animate-spin relative top-[1px]" />}
            <span>Invoices</span>
          </button>
        </div>
      </div>

      {successfulSubscription && <p className="text-black mb-5 font-bold">You have successfully subscribed.</p>}
      {user?.is_subscribed ? (
        <>
          {!user?.scheduled_cancel_at ? (
            <>
              <h5 className="mb-5">Your subscription is active</h5>
              <p className="text-gray-800">Next billing date is {dayjs(user.current_period_end).format('LLL')}</p>
              <button onClick={() => setModalOpen(true)} className="text-sm text-gray-400 mt-1">
                Click here to cancel your subscription
              </button>
            </>
          ) : (
            <>
              <span className="text-lg text-black mb-2">
                Your subscription is scheduled to cancel at {dayjs(user.scheduled_cancel_at).format('LLL')}.
              </span>
              <button onClick={resumeSubscription} className="primary rounded-lg mt-2">
                Click here to resume
              </button>
            </>
          )}
        </>
      ) : (
        !(showPaymentForm && clientSecret) && (
          <>
            <span className="text-md text-gray-400 mb-2">You don&apos;t have an active subscription.</span>
            <button onClick={subscribe} className="primary rounded-lg">
              Click here to subscribe
            </button>
          </>
        )
      )}
      {showPaymentForm && clientSecret && <SubscribePaymentForm clientSecret={clientSecret} />}

      <Modal
        open={modalOpen}
        onClose={() => setModalOpen(false)}
        center
        showCloseIcon={false}
        classNames={{ modal: '!max-w-[90%] w-[500px]' }}>
        <h2 className="text-lg text-center tracking-normal w-full">
          Are you sure you want to cancel your subscription?
        </h2>
        <div className="flex w-full justify-center gap-5 mt-5">
          <button
            onClick={() => {
              cancelSubscription();
              setModalOpen(false);
              setProcessingUpdate(true);
            }}
            className="secondary !p-3 rounded-md w-[120px]">
            Yes, cancel
          </button>
          <button
            onClick={() => {
              setModalOpen(false);
            }}
            className="primary !p-3 rounded-md w-[120px]">
            No
          </button>
        </div>
      </Modal>

      {processingUpdate && (
        <Modal
          open={processingUpdate}
          showCloseIcon={false}
          onClose={() => {}}
          center
          classNames={{ modal: '!max-w-[90%] w-[500px]' }}>
          <h2 className="text-lg tracking-normal w-[calc(100%-20px)] text-center">Please wait...</h2>
        </Modal>
      )}
    </div>
  );
}

export default requireAuth(Subscription, []);
