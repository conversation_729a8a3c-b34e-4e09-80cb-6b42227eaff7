'use client';

// Styles
import 'react-responsive-modal/styles.css';
import { useContext, useEffect, useState } from 'react';
import { useTranslations } from 'next-intl';
import dayjs from 'dayjs';
import localizedFormat from 'dayjs/plugin/localizedFormat';
import { httpsCallable } from 'firebase/functions';
import { Loader2 } from 'lucide-react';
import { Modal } from 'react-responsive-modal';
import { useRouter } from '@/lib/i18n/navigation';
import { auth, functions } from '@/utils/firebase';
import SubscribePaymentForm from '@/components/SubscrbePaymentForm';
import requireAuth from '@/components/requireAuth';
import { UserContext } from '@/store/UserContext';
import SessionContext from '@/store/SessionContext';

dayjs.extend(localizedFormat);

function Subscription() {
  const t = useTranslations('members_subscription');
  const router = useRouter();
  const successfulSubscription = new URLSearchParams(window.location.search).get('success');
  const { paymentSystem } = useContext(SessionContext);
  const { user } = useContext(UserContext);
  const { updateStripeInvoices, updateSolidgateInvoices } = useContext(SessionContext);
  const [modalOpen, setModalOpen] = useState(false);
  const [processingUpdate, setProcessingUpdate] = useState(false);
  const [showPaymentForm, setShowPaymentForm] = useState(false);
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [loadingCustomerPortal, setLoadingCustomerPortal] = useState(false);

  async function cancelSubscription() {
    try {
      setProcessingUpdate(true);
      const cancel = httpsCallable(
        functions,
        user?.activeSubscriptionType === 'solidgate' ? 'cancelSubscriptionSolidgate' : 'cancelSubscription'
      );
      await cancel();
      await auth.currentUser?.getIdToken(true);
    } catch (error) {
      console.error('Error canceling subscription:', error);
    } finally {
      setProcessingUpdate(false);
    }
  }

  async function resumeSubscription() {
    try {
      setProcessingUpdate(true);
      const resume = httpsCallable(
        functions,
        user?.activeSubscriptionType === 'solidgate' ? 'resumeSubscriptionSolidgate' : 'resumeSubscription'
      );
      await resume();
      await auth.currentUser?.getIdToken(true);
    } catch (error) {
      console.error('Error resuming subscription:', error);
    } finally {
      setProcessingUpdate(false);
    }
  }

  async function subscribe() {
    try {
      setProcessingUpdate(true);
      const create = httpsCallable(functions, 'createSubscription');
      const result: any = await create();
      if (!result.data.success) {
        setClientSecret(result.data.clientSecret);
        setShowPaymentForm(true);
      } else {
        window.location = '/user/subscription?success=true' as any;
      }
    } catch (error) {
      console.error('Error creating subscription:', error);
    } finally {
      setProcessingUpdate(false);
    }
  }

  async function goToSelfServicePortal() {
    if (loadingCustomerPortal) return;
    try {
      setLoadingCustomerPortal(true);
      const createPortalSession = httpsCallable(functions, 'createPortalSession');
      const result: any = await createPortalSession();
      window.location = result.data.url;
    } catch (error) {
      console.error('Error accessing customer portal:', error);
      setLoadingCustomerPortal(false);
    }
  }

  const goToInvoices = async () => {
    try {
      const isSolidgate = process.env.NEXT_PUBLIC_USE_PAYMENT === 'solidgate';
      const getInvoices = httpsCallable(functions, isSolidgate ? 'getSolidgateInvoicesInfo' : 'getStripeInvoicesInfo');
      const invoices: any = await getInvoices({ email: user?.email });
      isSolidgate ? updateSolidgateInvoices(invoices) : updateStripeInvoices(invoices);
      router.push('/invoices');
    } catch (error) {
      console.error('Error fetching invoices:', error);
    }
  };

  useEffect(() => {
    auth.currentUser?.getIdToken(true);
  }, [user]);

  useEffect(() => {
    const clientSecret = new URLSearchParams(window.location.search).get('payment_intent_client_secret');
    if (clientSecret) {
      setClientSecret(clientSecret);
      setShowPaymentForm(true);
    }
  }, []);

  return (
    <div className="flex flex-col items-center mt-10">
      <h1 className="fs-title mb-2 text-[32px] leading-[112.5%]">{t('title')}</h1>

      <div className="mt-1 border-b w-full mb-10 flex justify-center md:justify-end pb-2">
        <div className="flex flex-col items-end">
          {paymentSystem !== 'solidgate' && (
            <button
              onClick={goToSelfServicePortal}
              className="text-primary hover:opacity-90 font-semibold text-sm flex gap-1">
              {loadingCustomerPortal && <Loader2 className="h-4 w-4 animate-spin relative top-[1px]" />}
              <span>{t('btn_manage_payment_methods')}</span>
            </button>
          )}
          <button onClick={goToInvoices} className="text-primary hover:opacity-90 inline-flex font-semibold text-sm">
            {loadingCustomerPortal && <Loader2 className="h-4 w-4 animate-spin relative top-[1px]" />}
            <span>{t('btn_invoices')}</span>
          </button>
        </div>
      </div>

      {successfulSubscription && <p className="text-black mb-5 font-bold">{t('success_message')}</p>}
      {user?.is_subscribed ? (
        <>
          {!user?.scheduled_cancel_at ? (
            <>
              <h5 className="mb-5">{t('active_subscription')}</h5>
              <p className="text-gray-800">{t('next_billing_date', { date: dayjs(user.current_period_end).format('LLL') })}</p>
              <button onClick={() => setModalOpen(true)} className="text-sm text-gray-400 mt-1">
                {t('btn_cancel_subscription')}
              </button>
            </>
          ) : (
            <>
              <span className="text-lg text-black mb-2">
                {t('scheduled_cancel_message', { date: dayjs(user.scheduled_cancel_at).format('LLL') })}
              </span>
              <button onClick={resumeSubscription} className="primary rounded-lg mt-2">
                {t('btn_resume_subscription')}
              </button>
            </>
          )}
        </>
      ) : (
        !(showPaymentForm && clientSecret) && (
          <>
            <span className="text-md text-gray-400 mb-2">{t('no_active_subscription')}</span>
            <button onClick={subscribe} className="primary rounded-lg">
              {t('btn_subscribe')}
            </button>
          </>
        )
      )}
      {showPaymentForm && clientSecret && <SubscribePaymentForm clientSecret={clientSecret} />}

      <Modal
        open={modalOpen}
        onClose={() => setModalOpen(false)}
        center
        showCloseIcon={false}
        classNames={{ modal: '!max-w-[90%] w-[500px]' }}>
        <h2 className="text-lg text-center tracking-normal w-full">
          {t('cancel_modal.title')}
        </h2>
        <div className="flex w-full justify-center gap-5 mt-5">
          <button
            onClick={() => {
              setModalOpen(false);
              cancelSubscription();
            }}
            className="secondary !p-3 rounded-md w-[120px]">
            {t('cancel_modal.btn_yes_cancel')}
          </button>
          <button
            onClick={() => {
              setModalOpen(false);
            }}
            className="primary !p-3 rounded-md w-[120px]">
            {t('cancel_modal.btn_no')}
          </button>
        </div>
      </Modal>

      {processingUpdate && (
        <Modal
          open={processingUpdate}
          showCloseIcon={false}
          onClose={() => {}}
          center
          classNames={{ modal: '!max-w-[90%] w-[500px]' }}>
          <h2 className="text-lg tracking-normal w-[calc(100%-20px)] text-center">{t('processing_modal.title')}</h2>
        </Modal>
      )}
    </div>
  );
}

export default requireAuth(Subscription, []);
