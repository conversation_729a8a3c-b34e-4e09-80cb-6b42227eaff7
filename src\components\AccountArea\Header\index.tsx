'use client'

import React from 'react';
import '@/sass/form.scss';
import AccountAreaMenu from '@/components/AccountArea/Menu';

const AccountAreaHeader = () => {
  return (
    <div
      className='header w-full flex justify-center max-w-[100vw] mt-5'
    >
      <div className='w-full flex flex-col items-start gap-5 xxs:gap-0 xxs:flex-no-wrap justify-between 2xl:max-w-[calc(1440px)] m-auto'>
        <h3 className='ml-7'>My account</h3>
        <AccountAreaMenu className='!hidden lg:!grid' {...{ position: 'header' }} />
      </div>
    </div>
  );
};

export default AccountAreaHeader;
