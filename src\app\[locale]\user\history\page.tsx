'use client';

import { useState, useEffect, useContext } from 'react';
import { useTranslations } from 'next-intl';
import { useRouter } from '@/lib/i18n/navigation';
import { httpsCallable } from 'firebase/functions';
import { Loader2 } from 'lucide-react';
import dayjs from 'dayjs';
import localizedFormat from 'dayjs/plugin/localizedFormat';
import requireAuth from '@/components/requireAuth';
import SessionContext from '@/store/SessionContext';
import { functions } from '@/utils/firebase';
import '@/sass/spinner.scss';
dayjs.extend(localizedFormat);

type GeneratedData = {
  success: boolean;
  sessionId: string;
};

type HistoryItem = {
  iq: number;
  date: string;
  type?: string;
  loveScore?: { category: string; sum: number; percentage: string }[];
  emotionalScores?: { [key: string]: number };
  answers?: any;
};

const TestHistory = () => {
  const t = useTranslations('members_test_history');
  const [history, setHistory] = useState([]);
  const [loadingRow, setLoadingRow] = useState<string | null>(null);
  const getTestHistory = httpsCallable(functions, 'getTestHistory');
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const rowsPerPage = 10;
  const { updateEmotionalScores, updateResults, updateAllAnswers } = useContext(SessionContext);

  const showResult = async (rowData: HistoryItem, type?: string) => {
    if (rowData.type === 'emotional-intelligence') {
      updateEmotionalScores(rowData.emotionalScores);
      router.push('/emotional-intelligence/results?showResults=true');
    } else if (rowData.type === 'love-languages') {
      updateResults(rowData.loveScore);
      router.push('/love-languages/results?showResults=true');
    } else {
      setLoadingRow(rowData.date);
      updateAllAnswers(rowData?.answers);
      const generatedData: GeneratedData = await generateReportAndCertificate(rowData?.answers, true);

      if (type === 'report') {
        const reportUrl = `${process.env.NEXT_PUBLIC_FILES_BUCKET}/${generatedData.sessionId}/report.pdf`;
        window.open(reportUrl, '_blank');
      } else {
        const certificateUrl = `${process.env.NEXT_PUBLIC_FILES_BUCKET}/${generatedData.sessionId}/certificate.pdf`;
        window.open(certificateUrl, '_blank');
      }
      // router.push('/calculation?showResults=true');
    }
    setLoadingRow(null);
  };

  async function generateReportAndCertificate(answers: any, showResults: boolean): Promise<GeneratedData> {
    const generate = httpsCallable(functions, 'generateReportAndCertificate');
    const result = await generate({ answers, showResults });
    const data = result.data as GeneratedData;
    return data;
  }

  useEffect(() => {
    getTestHistory()
      .then((res: any) => {
        const filteredHistory = res.data.history.reverse().filter((item: HistoryItem) => {
          if (item.type === 'emotional-intelligence') return !!item.emotionalScores;
          if (item.type === 'love-languages') return !!item.loveScore;
          return true;
        });
        setHistory(filteredHistory);
        setLoading(false);
      })
      .catch(e => {
        console.error(e);
      });
  }, [getTestHistory]);

  const formatType = (str?: string) => {
    if (!str) return '';
    const noDashes = str.replace(/-/g, ' ');
    return noDashes.charAt(0).toUpperCase() + noDashes.slice(1);
  };

  const computeScore = (item: HistoryItem) => {
    if (item.type === 'love-languages' && item.loveScore) {
      return item.loveScore[0].category;
    } else if (item.type === 'emotional-intelligence' && item.emotionalScores) {
      return Object.values(item.emotionalScores).reduce((acc, curr) => acc + curr, 0);
    } else {
      return item.iq;
    }
  };

  const totalPages = Math.ceil(history.length / rowsPerPage);
  const lastIndex = currentPage * rowsPerPage;
  const firstIndex = lastIndex - rowsPerPage;
  const currentItems = history.slice(firstIndex, lastIndex);
  const pageNumbers = Array.from({ length: totalPages }, (_, i) => i + 1);

  return (
    <div className="flex mx-auto flex-col mt-10">
      <h1 className="fs-title mb-5 text-[32px] text-center">{t('title')}</h1>
      <table className="table-auto mt-5">
        <thead>
          <tr className="bg-gray-200">
            <th className="p-4 w-[200px] text-center">{t('table.headers.date')}</th>
            <th className="p-4 w-[200px] text-center">{t('table.headers.type')}</th>
            <th className="p-4 w-[200px] text-center">{t('table.headers.score')}</th>
            <th className="p-4 w-[200px] text-center">{t('table.headers.results')}</th>
          </tr>
        </thead>
        <tbody>
          {loading && currentItems.length === 0 && (
            <tr>
              <td colSpan={4} className="p-4 text-center">
                {t('table.loading')}
              </td>
            </tr>
          )}
          {!loading &&
            currentItems.map((item: any) => {
              const testType = formatType(item.type);
              const score = computeScore(item);
              return (
                <tr key={item.date}>
                  <td className="p-4 text-center">{dayjs(item.date).format('lll')}</td>
                  <td className="p-4 text-center">{testType || t('table.test_types.iq')}</td>
                  <td className="p-4 text-center">{score}</td>
                  <td className="p-4 text-center text-[#ff932f]">
                    {item.type === 'love-languages' || item.type === 'emotional-intelligence' ? (
                      <p className="text-[#ff932f] text-[16px] cursor-pointer" onClick={() => showResult(item)}>
                        {t('table.actions.show_result')}
                      </p>
                    ) : loadingRow === item.date ? (
                      <div className="flex justify-center">
                        <Loader2 className="h-4 w-4 animate-spin m-[5px] " />
                      </div>
                    ) : (
                      <div className="flex justify-center items-center space-x-2">
                        <p
                          className="text-[#ff932f] text-[16px] cursor-pointer"
                          onClick={() => showResult(item, 'report')}>
                          {t('table.actions.report')}
                        </p>
                        <span className="text-[#ff932f] text-[16px]">•</span> {/* Bullet dot here */}
                        <p
                          className="text-[#ff932f] text-[16px] cursor-pointer"
                          onClick={() => showResult(item, 'certificate')}>
                          {t('table.actions.certificate')}
                        </p>
                      </div>
                    )}
                  </td>
                </tr>
              );
            })}
        </tbody>
      </table>
      {totalPages > 1 && (
        <div className="flex justify-center items-center mt-4 space-x-2">
          {pageNumbers.map(page => (
            <button
              key={page}
              onClick={() => setCurrentPage(page)}
              className={`px-3 py-1 cursor-pointer ${currentPage === page ? 'text-[#ff932f] text-[16px]' : ''}`}>
              {page}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default requireAuth(TestHistory, []);
