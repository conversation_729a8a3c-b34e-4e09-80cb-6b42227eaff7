'use client';

import Image from 'next/image';
import { memo, useContext } from 'react';
import { Link as I18nLink, usePathname } from '@/lib/i18n/navigation';
import Link from 'next/link';
import { menuItems, legalItems, menuItemTranslationKeys, legalMenuTranslationKeys } from '@/app/config';
import { UserContext } from '@/store/UserContext';
import SessionContext from '@/store/SessionContext';
import Copyright from '@/components/Footer/Copyright';
import { useTranslations } from 'next-intl';

const Footer = () => {
  const t = useTranslations('footer');
  const tNav = useTranslations();
  const activePath = usePathname();
  const { user } = useContext(UserContext);
  const { siteConfig } = useContext(SessionContext);

  return (
    <>
      {![
        '/form',
        '/calculation',
        '/checkout',
        '/checkout-v2',
        '/checkout-v2/upsell',
        '/mobile-checkout',
        '/questions',
      ].includes(activePath) && (
        <div className="w-full font-segoe">
          <div className="flex flex-col max-w-[1280px] mx-[16px] my-[24px] md:mx-auto md:my-[48px]">
            <div className="flex flex-col md:flex-row w-full gap-[24px] md:justify-between border-b border-gray-500 pb-[24px]">
              <Link href="/" className="h-fit">
                <Image
                  className="w-[216.81px] h-[26.21px] md:w-[300.2px] md:h-[33.52px]"
                  src={siteConfig.logo.path}
                  alt="Logo"
                  width={siteConfig.logo.width}
                  height={siteConfig.logo.height}
                  quality={100}
                  priority
                />
              </Link>
              <div className="flex flex-col md:flex-row gap-[16px] md:gap-[100px] ">
                <div className="flex flex-col font-semibold text-[18px] leading-[27px] tracking-[0] text-[#000]">
                  <Link href="/support" className="mb-[15.5px]">
                    {t('support.title')}
                  </Link>
                  <span className="mb-[12.5px]">{t('support.need_help')}</span>
                  <div className="border-2 border-gray-200 rounded-[10px] py-[10px] pl-[18.91px] pr-[17.62px] w-fit">
                    <Link href="/support" className="flex flex-row gap-[11.95px]">
                      <Image
                        src="/images/footer/receiver.svg"
                        alt="receiver"
                        width={25}
                        height={25}
                        quality={100}
                        priority
                      />
                      <div className="flex flex-col text-[14px] leading-[21px] text-left">
                        <span>{t('support.customer_support.title')}</span>
                        <span>{t('support.customer_support.availability')}</span>
                      </div>
                    </Link>
                  </div>
                </div>
                <div className="flex flex-col md:flex-row gap-[16px] md:gap-[80px] font-semibold tracking-[0]">
                  <div className="flex flex-col gap-[16px]">
                    <span className="text-[18px] leading-[27px]">{t('sections.legal.title')}</span>
                    <div className="flex flex-col gap-[8px] text-[16px] leading-[24px]">
                      {legalItems
                        .filter(x =>
                          !(activePath == '/results' && x.id == 'pricing') && !user ? !x.memberOnly : !x.hideIfLoggedIn
                        )
                        .map(item => {
                          const { id, path, title } = item;
                          // Use regular Link for external URLs
                          const LinkComponent = path.startsWith('http') ? Link : I18nLink;
                          return (
                            <LinkComponent key={id} href={`${path}`}>
                              {tNav(legalMenuTranslationKeys[id] || title)}
                            </LinkComponent>
                          );
                        })}
                    </div>
                  </div>
                  <div className="flex flex-col gap-[16px]">
                    <span className="text-[18px] leading-[27px]">{t('sections.menu.title')}</span>
                    <div className="flex flex-col gap-[8px] text-[16px] leading-[24px]">
                      {menuItems
                        .filter(x =>
                          !(activePath == '/results' && x.id == 'pricing') && !user ? !x.memberOnly : !x.hideIfLoggedIn
                        )
                        .map(item => {
                          const { id, path, title } = item;
                          // Use regular Link for insights to bypass internationalization
                          const LinkComponent = id === 'insights' ? Link : I18nLink;
                          return (
                            <LinkComponent key={id} href={`${path}`}>
                              {tNav(menuItemTranslationKeys[id] || title)}
                            </LinkComponent>
                          );
                        })}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex flex-col md:flex-row gap-[16px] md:gap-[49px] py-0 md:py-[4px] mt-[24px]">
              <Copyright />
              <div className="flex flex-row gap-[6px]">
                <Image
                  src="/images/footer/card1.svg"
                  alt={t('payment_methods.card1')}
                  width={46}
                  height={32}
                  quality={100}
                  priority
                  className="rounded-[8px] bg-white"
                />
                <Image
                  src="/images/footer/card2.svg"
                  alt={t('payment_methods.card2')}
                  width={46}
                  height={32}
                  quality={100}
                  priority
                  className="rounded-[8px] bg-white"
                />
                <Image
                  src="/images/footer/card3.svg"
                  alt={t('payment_methods.card3')}
                  width={46}
                  height={32}
                  quality={100}
                  priority
                  className="rounded-[8px] bg-white"
                />
                <Image
                  src="/images/footer/card4.svg"
                  alt={t('payment_methods.card4')}
                  width={46}
                  height={32}
                  quality={100}
                  priority
                  className="rounded-[8px] bg-white"
                />
                <Image
                  src="/images/footer/card5.svg"
                  alt={t('payment_methods.card5')}
                  width={46}
                  height={32}
                  quality={100}
                  priority
                  className="rounded-[8px] bg-white"
                />
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default memo(Footer);
