'use client';

import Image from 'next/image';
import { memo, useContext } from 'react';
import { Link } from '@/lib/i18n/navigation';
import { useTranslations } from 'next-intl';
import { usePathname } from '@/lib/i18n/navigation';
import SessionContext from '@/store/SessionContext';
import Copyright from '@/components/Footer/Copyright';
import PaymentMethods from '@/components/Footer/PaymentMethods';
import LegalMenu from '@/components/Footer/LegalMenu';
import MainMenu from '@/components/Footer/MainMenu';
import SupportSection from '@/components/Footer/SupportSection';

const Footer = () => {
  const t = useTranslations('footer');
  const activePath = usePathname();
  const { siteConfig } = useContext(SessionContext);

  return (
    <>
      {![
        '/form',
        '/calculation',
        '/checkout',
        '/checkout-v2',
        '/checkout-v2/upsell',
        '/mobile-checkout',
        '/questions',
      ].includes(activePath) && (
        <div className="w-full font-segoe">
          <div className="flex flex-col max-w-[1280px] mx-[16px] my-[24px] md:mx-auto md:my-[48px]">
            <div className="flex flex-col md:flex-row w-full gap-[24px] md:justify-between border-b border-gray-500 pb-[24px]">
              <Link href="/" className="h-fit">
                <Image
                  className="w-[216.81px] h-[26.21px] md:w-[300.2px] md:h-[33.52px]"
                  src={siteConfig.logo.path}
                  alt="Logo"
                  width={siteConfig.logo.width}
                  height={siteConfig.logo.height}
                  quality={100}
                  priority
                />
              </Link>
              <div className="flex flex-col md:flex-row gap-[16px] md:gap-[100px] ">
                <SupportSection />
                <div className="flex flex-col md:flex-row gap-[16px] md:gap-[80px] font-semibold tracking-[0]">
                  <LegalMenu />
                  <MainMenu />
                </div>
              </div>
            </div>
            <div className="flex flex-col md:flex-row gap-[16px] md:gap-[49px] py-0 md:py-[4px] mt-[24px]">
              <Copyright />
              <PaymentMethods />
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default memo(Footer);
